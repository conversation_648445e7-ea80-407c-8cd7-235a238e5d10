"""
وحدة التحكم في نظام الرش المحوري
Central Spray System Controller Module
"""

import time
import threading
from typing import Dict, List, Optional
import logging
from datetime import datetime

try:
    import RPi.GPIO as GPIO
    GPIO_AVAILABLE = True
except ImportError:
    GPIO_AVAILABLE = False
    print("تحذير: مكتبة GPIO غير متوفرة - سيتم استخدام وضع المحاكاة")

from motor_controller import MotorController
from config import Config

class SprayController:
    """فئة التحكم الرئيسية في نظام الرش المحوري"""
    
    def __init__(self):
        self.logger = logging.getLogger('SprayController')
        
        # إعداد المحركات
        self.motors = {}
        for motor_name, pins in Config.MOTOR_PINS.items():
            self.motors[motor_name] = MotorController(motor_name, pins)
        
        # إعداد منافذ الرش
        self.spray_pins = Config.SPRAY_PINS
        self.spray_status = {pin_name: False for pin_name in self.spray_pins.keys()}
        
        # حالة النظام
        self.system_active = False
        self.emergency_stop_active = False
        self.operation_start_time = None
        
        # خيط مراقبة الأمان
        self.safety_thread = None
        self.safety_stop_event = threading.Event()
        
        if GPIO_AVAILABLE:
            self._setup_spray_gpio()
            self._setup_emergency_stop()
        
        self.logger.info("تم تهيئة نظام التحكم في الرش المحوري")
    
    def _setup_spray_gpio(self):
        """إعداد منافذ GPIO للرش"""
        for pin in self.spray_pins.values():
            GPIO.setup(pin, GPIO.OUT)
            GPIO.output(pin, GPIO.LOW)
        self.logger.info("تم إعداد منافذ GPIO للرش")
    
    def _setup_emergency_stop(self):
        """إعداد زر الإيقاف الطارئ"""
        GPIO.setup(Config.EMERGENCY_STOP_PIN, GPIO.IN, pull_up_down=GPIO.PUD_UP)
        GPIO.add_event_detect(
            Config.EMERGENCY_STOP_PIN, 
            GPIO.FALLING, 
            callback=self._emergency_stop_callback,
            bouncetime=300
        )
        self.logger.info("تم إعداد زر الإيقاف الطارئ")
    
    def _emergency_stop_callback(self, channel):
        """استدعاء الإيقاف الطارئ"""
        self.emergency_stop()
    
    def start_system(self):
        """بدء تشغيل النظام"""
        if self.emergency_stop_active:
            self.logger.error("لا يمكن بدء النظام - الإيقاف الطارئ مفعل")
            return False
        
        self.system_active = True
        self.operation_start_time = datetime.now()
        
        # بدء خيط مراقبة الأمان
        self.safety_stop_event.clear()
        self.safety_thread = threading.Thread(target=self._safety_monitor)
        self.safety_thread.start()
        
        # تفعيل جميع المحركات
        for motor in self.motors.values():
            motor.enable()
        
        self.logger.info("تم بدء تشغيل النظام")
        return True
    
    def stop_system(self):
        """إيقاف النظام"""
        self.system_active = False
        
        # إيقاف جميع المحركات
        for motor in self.motors.values():
            motor.stop()
            motor.disable()
        
        # إيقاف جميع المرشات
        self.stop_all_spray()
        
        # إيقاف خيط مراقبة الأمان
        if self.safety_thread:
            self.safety_stop_event.set()
            self.safety_thread.join(timeout=2)
        
        self.logger.info("تم إيقاف النظام")
    
    def emergency_stop(self):
        """إيقاف طارئ للنظام"""
        self.emergency_stop_active = True
        self.system_active = False
        
        # إيقاف طارئ لجميع المحركات
        for motor in self.motors.values():
            motor.emergency_stop()
        
        # إيقاف جميع المرشات
        self.stop_all_spray()
        
        self.logger.warning("تم تفعيل الإيقاف الطارئ")
    
    def reset_emergency_stop(self):
        """إعادة تعيين الإيقاف الطارئ"""
        self.emergency_stop_active = False
        self.logger.info("تم إعادة تعيين الإيقاف الطارئ")
    
    def _safety_monitor(self):
        """مراقب الأمان"""
        while not self.safety_stop_event.is_set():
            if self.system_active and self.operation_start_time:
                elapsed_time = (datetime.now() - self.operation_start_time).total_seconds()
                if elapsed_time > Config.MAX_OPERATION_TIME:
                    self.logger.warning("تم تجاوز الحد الأقصى لوقت التشغيل - إيقاف النظام")
                    self.stop_system()
                    break
            
            time.sleep(1)
    
    def move_motor(self, motor_name: str, steps: int, speed: Optional[int] = None) -> bool:
        """تحريك محرك محدد"""
        if not self.system_active:
            self.logger.error("النظام غير مفعل")
            return False
        
        if motor_name not in self.motors:
            self.logger.error(f"المحرك {motor_name} غير موجود")
            return False
        
        return self.motors[motor_name].move_steps(steps, speed)
    
    def move_motor_to_position(self, motor_name: str, position: int, speed: Optional[int] = None) -> bool:
        """تحريك محرك إلى موضع محدد"""
        if not self.system_active:
            self.logger.error("النظام غير مفعل")
            return False
        
        if motor_name not in self.motors:
            self.logger.error(f"المحرك {motor_name} غير موجود")
            return False
        
        return self.motors[motor_name].move_to_position(position, speed)
    
    def stop_motor(self, motor_name: str) -> bool:
        """إيقاف محرك محدد"""
        if motor_name not in self.motors:
            self.logger.error(f"المحرك {motor_name} غير موجود")
            return False
        
        self.motors[motor_name].stop()
        return True
    
    def start_spray(self, spray_name: str) -> bool:
        """بدء الرش من مرشة محددة"""
        if not self.system_active:
            self.logger.error("النظام غير مفعل")
            return False
        
        if spray_name not in self.spray_pins:
            self.logger.error(f"المرشة {spray_name} غير موجودة")
            return False
        
        if GPIO_AVAILABLE:
            GPIO.output(self.spray_pins[spray_name], GPIO.HIGH)
        
        self.spray_status[spray_name] = True
        self.logger.info(f"تم بدء الرش من {spray_name}")
        return True
    
    def stop_spray(self, spray_name: str) -> bool:
        """إيقاف الرش من مرشة محددة"""
        if spray_name not in self.spray_pins:
            self.logger.error(f"المرشة {spray_name} غير موجودة")
            return False
        
        if GPIO_AVAILABLE:
            GPIO.output(self.spray_pins[spray_name], GPIO.LOW)
        
        self.spray_status[spray_name] = False
        self.logger.info(f"تم إيقاف الرش من {spray_name}")
        return True
    
    def stop_all_spray(self):
        """إيقاف جميع المرشات"""
        for spray_name in self.spray_pins.keys():
            self.stop_spray(spray_name)
        self.logger.info("تم إيقاف جميع المرشات")
    
    def get_system_status(self) -> Dict:
        """الحصول على حالة النظام الكاملة"""
        motor_status = {}
        for name, motor in self.motors.items():
            motor_status[name] = motor.get_status()
        
        return {
            'system_active': self.system_active,
            'emergency_stop_active': self.emergency_stop_active,
            'operation_start_time': self.operation_start_time.isoformat() if self.operation_start_time else None,
            'motors': motor_status,
            'spray_status': self.spray_status.copy(),
            'timestamp': datetime.now().isoformat()
        }
    
    def cleanup(self):
        """تنظيف الموارد"""
        self.stop_system()
        
        for motor in self.motors.values():
            motor.cleanup()
        
        if GPIO_AVAILABLE:
            GPIO.cleanup()
        
        self.logger.info("تم تنظيف جميع موارد النظام")

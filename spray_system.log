2025-06-02 03:26:22,235 - MotorController.motor_1 - WARNING - تشغيل في وضع المحاكاة - GP<PERSON> غير متوفر
2025-06-02 03:26:22,249 - MotorController.motor_2 - WARNING - تشغيل في وضع المحاكاة - <PERSON><PERSON> <PERSON>ير متوفر
2025-06-02 03:26:22,264 - MotorController.motor_3 - WARNING - تشغيل في وضع المحاكاة - GPIO غير متوفر
2025-06-02 03:26:22,285 - SprayController - INFO - تم تهيئة نظام التحكم في الرش المحوري
2025-06-02 03:26:22,362 - FlaskApp - INFO - بدء تشغيل خادم نظام الرش المحوري
2025-06-02 03:26:22,543 - werkzeug - INFO -  * Restarting with stat
2025-06-02 03:26:32,699 - MotorController.motor_1 - WARNING - تشغيل في وضع المحاكاة - GPIO غير متوفر
2025-06-02 03:26:32,702 - MotorController.motor_2 - WARNING - تشغيل في وضع المحاكاة - GPIO غير متوفر
2025-06-02 03:26:32,704 - MotorController.motor_3 - WARNING - تشغيل في وضع المحاكاة - GPIO غير متوفر
2025-06-02 03:26:32,712 - SprayController - INFO - تم تهيئة نظام التحكم في الرش المحوري
2025-06-02 03:26:33,163 - FlaskApp - INFO - بدء تشغيل خادم نظام الرش المحوري
2025-06-02 03:26:33,262 - werkzeug - WARNING -  * Debugger is active!
2025-06-02 03:26:33,428 - werkzeug - INFO -  * Debugger PIN: 211-370-091
2025-06-02 03:27:08,742 - FlaskApp - INFO - عميل متصل
2025-06-02 03:27:55,259 - MotorController.motor_1 - INFO - تم تفعيل المحرك motor_1
2025-06-02 03:27:55,260 - MotorController.motor_2 - INFO - تم تفعيل المحرك motor_2
2025-06-02 03:27:55,262 - MotorController.motor_3 - INFO - تم تفعيل المحرك motor_3
2025-06-02 03:27:55,294 - SprayController - INFO - تم بدء تشغيل النظام
2025-06-02 03:27:55,422 - FlaskApp - INFO - تم بدء بث حالة النظام
2025-06-02 03:28:09,375 - SprayController - ERROR - المحرك None غير موجود
2025-06-02 03:28:17,579 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:28:20,115 - FlaskApp - INFO - عميل متصل
2025-06-02 03:28:40,753 - MotorController.motor_1 - INFO - تم تحديد اتجاه المحرك motor_1: أمام
2025-06-02 03:28:40,801 - MotorController.motor_1 - INFO - بدء تحريك المحرك motor_1: 100 خطوة
2025-06-02 03:28:43,835 - MotorController.motor_1 - INFO - انتهاء حركة المحرك motor_1 - الموضع الحالي: 100
2025-06-02 03:28:51,379 - MotorController.motor_1 - INFO - تم تحديد اتجاه المحرك motor_1: أمام
2025-06-02 03:28:52,007 - MotorController.motor_1 - INFO - بدء تحريك المحرك motor_1: 100 خطوة
2025-06-02 03:28:55,247 - MotorController.motor_1 - WARNING - المحرك motor_1 يعمل بالفعل
2025-06-02 03:29:03,747 - MotorController.motor_2 - INFO - تم تحديد اتجاه المحرك motor_2: أمام
2025-06-02 03:29:04,196 - MotorController.motor_2 - INFO - بدء تحريك المحرك motor_2: 100 خطوة
2025-06-02 03:29:06,013 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:29:06,969 - MotorController.motor_1 - INFO - انتهاء حركة المحرك motor_1 - الموضع الحالي: 200
2025-06-02 03:29:07,945 - MotorController.motor_2 - INFO - انتهاء حركة المحرك motor_2 - الموضع الحالي: 100
2025-06-02 03:29:13,394 - FlaskApp - INFO - عميل متصل
2025-06-02 03:29:14,400 - MotorController.motor_3 - INFO - تم تحديد اتجاه المحرك motor_3: أمام
2025-06-02 03:29:16,663 - MotorController.motor_3 - INFO - بدء تحريك المحرك motor_3: 100 خطوة
2025-06-02 03:29:19,715 - MotorController.motor_3 - INFO - انتهاء حركة المحرك motor_3 - الموضع الحالي: 100
2025-06-02 03:29:31,176 - MotorController.motor_3 - INFO - تم تحديد اتجاه المحرك motor_3: أمام
2025-06-02 03:29:33,850 - MotorController.motor_3 - INFO - بدء تحريك المحرك motor_3: 100 خطوة
2025-06-02 03:29:41,051 - MotorController.motor_3 - INFO - انتهاء حركة المحرك motor_3 - الموضع الحالي: 200
2025-06-02 03:29:56,170 - SprayController - INFO - تم بدء الرش من pump
2025-06-02 03:29:58,900 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:30:04,924 - SprayController - INFO - تم بدء الرش من valve_1
2025-06-02 03:30:05,124 - FlaskApp - INFO - عميل متصل
2025-06-02 03:30:09,920 - SprayController - INFO - تم بدء الرش من valve_2
2025-06-02 03:30:14,729 - SprayController - INFO - تم بدء الرش من valve_3
2025-06-02 03:30:17,730 - SprayController - INFO - تم بدء الرش من valve_4
2025-06-02 03:30:50,850 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:30:52,923 - FlaskApp - INFO - عميل متصل
2025-06-02 03:31:03,663 - SprayController - INFO - تم إعادة تعيين الإيقاف الطارئ
2025-06-02 03:31:20,523 - MotorController.motor_1 - WARNING - إيقاف طارئ للمحرك motor_1
2025-06-02 03:31:20,651 - MotorController.motor_2 - WARNING - إيقاف طارئ للمحرك motor_2
2025-06-02 03:31:20,743 - MotorController.motor_3 - WARNING - إيقاف طارئ للمحرك motor_3
2025-06-02 03:31:20,746 - SprayController - INFO - تم إيقاف الرش من pump
2025-06-02 03:31:20,861 - SprayController - INFO - تم إيقاف الرش من valve_1
2025-06-02 03:31:20,912 - SprayController - INFO - تم إيقاف الرش من valve_2
2025-06-02 03:31:21,008 - SprayController - INFO - تم إيقاف الرش من valve_3
2025-06-02 03:31:21,073 - SprayController - INFO - تم إيقاف الرش من valve_4
2025-06-02 03:31:21,264 - SprayController - INFO - تم إيقاف جميع المرشات
2025-06-02 03:31:21,269 - SprayController - WARNING - تم تفعيل الإيقاف الطارئ
2025-06-02 03:31:21,295 - FlaskApp - INFO - تم إيقاف بث حالة النظام
2025-06-02 03:31:36,451 - SprayController - INFO - تم إعادة تعيين الإيقاف الطارئ
2025-06-02 03:31:38,412 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:31:41,018 - FlaskApp - INFO - عميل متصل
2025-06-02 03:31:52,992 - MotorController.motor_1 - INFO - تم تفعيل المحرك motor_1
2025-06-02 03:31:53,302 - MotorController.motor_2 - INFO - تم تفعيل المحرك motor_2
2025-06-02 03:31:53,303 - MotorController.motor_3 - INFO - تم تفعيل المحرك motor_3
2025-06-02 03:31:53,306 - SprayController - INFO - تم بدء تشغيل النظام
2025-06-02 03:31:53,390 - FlaskApp - INFO - تم بدء بث حالة النظام
2025-06-02 03:32:16,633 - SprayController - INFO - تم بدء الرش من pump
2025-06-02 03:32:23,899 - SprayController - INFO - تم بدء الرش من valve_1
2025-06-02 03:32:25,047 - SprayController - INFO - تم بدء الرش من valve_2
2025-06-02 03:32:26,626 - SprayController - INFO - تم بدء الرش من valve_3
2025-06-02 03:32:27,579 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:32:29,279 - FlaskApp - INFO - عميل متصل
2025-06-02 03:32:34,314 - SprayController - INFO - تم بدء الرش من valve_4
2025-06-02 03:32:41,198 - SprayController - INFO - تم بدء الرش من valve_4
2025-06-02 03:33:06,397 - MotorController.motor_2 - INFO - تم تحديد اتجاه المحرك motor_2: أمام
2025-06-02 03:33:06,771 - MotorController.motor_2 - INFO - بدء تحريك المحرك motor_2: 100 خطوة
2025-06-02 03:33:10,524 - MotorController.motor_2 - INFO - انتهاء حركة المحرك motor_2 - الموضع الحالي: 200
2025-06-02 03:33:14,431 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:33:15,276 - MotorController.motor_2 - INFO - تم تحديد اتجاه المحرك motor_2: أمام
2025-06-02 03:33:15,282 - MotorController.motor_2 - INFO - بدء تحريك المحرك motor_2: 100 خطوة
2025-06-02 03:33:16,119 - FlaskApp - INFO - عميل متصل
2025-06-02 03:33:17,943 - MotorController.motor_2 - INFO - انتهاء حركة المحرك motor_2 - الموضع الحالي: 300
2025-06-02 03:33:19,491 - MotorController.motor_2 - INFO - تم تحديد اتجاه المحرك motor_2: أمام
2025-06-02 03:33:19,495 - MotorController.motor_2 - INFO - بدء تحريك المحرك motor_2: 100 خطوة
2025-06-02 03:33:22,214 - MotorController.motor_2 - INFO - انتهاء حركة المحرك motor_2 - الموضع الحالي: 400
2025-06-02 03:34:01,612 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:34:04,269 - FlaskApp - INFO - عميل متصل
2025-06-02 03:34:15,090 - MotorController.motor_1 - INFO - تم تحديد اتجاه المحرك motor_1: خلف
2025-06-02 03:34:15,092 - MotorController.motor_1 - INFO - بدء تحريك المحرك motor_1: -196 خطوة
2025-06-02 03:34:21,503 - MotorController.motor_1 - INFO - انتهاء حركة المحرك motor_1 - الموضع الحالي: 4
2025-06-02 03:34:50,840 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:34:53,836 - MotorController.motor_2 - INFO - تم تحديد اتجاه المحرك motor_2: خلف
2025-06-02 03:34:54,351 - MotorController.motor_2 - INFO - بدء تحريك المحرك motor_2: -100 خطوة
2025-06-02 03:34:55,492 - MotorController.motor_2 - WARNING - المحرك motor_2 يعمل بالفعل
2025-06-02 03:34:56,681 - MotorController.motor_2 - WARNING - المحرك motor_2 يعمل بالفعل
2025-06-02 03:34:57,073 - FlaskApp - INFO - عميل متصل
2025-06-02 03:35:01,886 - MotorController.motor_2 - INFO - انتهاء حركة المحرك motor_2 - الموضع الحالي: 300
2025-06-02 03:35:04,635 - MotorController.motor_1 - INFO - تم تفعيل المحرك motor_1
2025-06-02 03:35:04,792 - MotorController.motor_2 - INFO - تم تفعيل المحرك motor_2
2025-06-02 03:35:05,717 - MotorController.motor_3 - INFO - تم تفعيل المحرك motor_3
2025-06-02 03:35:07,978 - SprayController - INFO - تم بدء تشغيل النظام
2025-06-02 03:35:09,679 - SprayController - INFO - تم إعادة تعيين الإيقاف الطارئ
2025-06-02 03:35:17,814 - SprayController - INFO - تم إعادة تعيين الإيقاف الطارئ
2025-06-02 03:35:28,332 - MotorController.motor_2 - INFO - تم تحديد اتجاه المحرك motor_2: خلف
2025-06-02 03:35:28,485 - MotorController.motor_2 - INFO - بدء تحريك المحرك motor_2: -100 خطوة
2025-06-02 03:35:38,080 - MotorController.motor_2 - INFO - انتهاء حركة المحرك motor_2 - الموضع الحالي: 200
2025-06-02 03:35:40,704 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:35:42,365 - FlaskApp - INFO - عميل متصل
2025-06-02 03:36:04,679 - MotorController.motor_1 - INFO - تم تحديد اتجاه المحرك motor_1: أمام
2025-06-02 03:36:07,444 - MotorController.motor_1 - INFO - بدء تحريك المحرك motor_1: 2 خطوة
2025-06-02 03:36:08,113 - MotorController.motor_1 - INFO - انتهاء حركة المحرك motor_1 - الموضع الحالي: 6
2025-06-02 03:36:16,964 - SprayController - ERROR - المحرك None غير موجود
2025-06-02 03:36:28,917 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:36:29,113 - MotorController.motor_1 - INFO - تم تفعيل المحرك motor_1
2025-06-02 03:36:33,300 - MotorController.motor_2 - INFO - تم تفعيل المحرك motor_2
2025-06-02 03:36:33,727 - MotorController.motor_3 - INFO - تم تفعيل المحرك motor_3
2025-06-02 03:36:33,963 - SprayController - INFO - تم بدء تشغيل النظام
2025-06-02 03:36:37,908 - MotorController.motor_1 - INFO - تم تحديد اتجاه المحرك motor_1: أمام
2025-06-02 03:36:38,476 - MotorController.motor_1 - INFO - بدء تحريك المحرك motor_1: 100 خطوة
2025-06-02 03:36:39,357 - MotorController.motor_1 - INFO - تم تفعيل المحرك motor_1
2025-06-02 03:36:39,363 - MotorController.motor_2 - INFO - تم تفعيل المحرك motor_2
2025-06-02 03:36:39,367 - MotorController.motor_3 - INFO - تم تفعيل المحرك motor_3
2025-06-02 03:36:39,413 - SprayController - INFO - تم بدء تشغيل النظام
2025-06-02 03:36:40,047 - FlaskApp - INFO - عميل متصل
2025-06-02 03:36:47,395 - MotorController.motor_1 - INFO - انتهاء حركة المحرك motor_1 - الموضع الحالي: 106
2025-06-02 03:37:05,409 - SprayController - INFO - تم إيقاف الرش من pump
2025-06-02 03:37:05,416 - SprayController - INFO - تم إيقاف الرش من valve_1
2025-06-02 03:37:05,419 - SprayController - INFO - تم إيقاف الرش من valve_2
2025-06-02 03:37:05,420 - SprayController - INFO - تم إيقاف الرش من valve_3
2025-06-02 03:37:05,426 - SprayController - INFO - تم إيقاف الرش من valve_4
2025-06-02 03:37:05,437 - SprayController - INFO - تم إيقاف جميع المرشات
2025-06-02 03:37:13,870 - SprayController - INFO - تم إيقاف الرش من pump
2025-06-02 03:37:13,892 - SprayController - INFO - تم إيقاف الرش من valve_1
2025-06-02 03:37:14,011 - SprayController - INFO - تم إيقاف الرش من valve_2
2025-06-02 03:37:14,044 - SprayController - INFO - تم إيقاف الرش من valve_3
2025-06-02 03:37:14,304 - SprayController - INFO - تم إيقاف الرش من valve_4
2025-06-02 03:37:14,308 - SprayController - INFO - تم إيقاف جميع المرشات
2025-06-02 03:37:25,245 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:37:27,975 - FlaskApp - INFO - عميل متصل
2025-06-02 03:38:12,822 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:38:16,326 - FlaskApp - INFO - عميل متصل
2025-06-02 03:38:49,166 - MotorController.motor_2 - INFO - تم تحديد اتجاه المحرك motor_2: خلف
2025-06-02 03:38:49,217 - MotorController.motor_2 - INFO - بدء تحريك المحرك motor_2: -197 خطوة
2025-06-02 03:38:51,929 - SprayController - ERROR - المحرك None غير موجود
2025-06-02 03:38:54,433 - SprayController - ERROR - المحرك None غير موجود
2025-06-02 03:39:01,368 - FlaskApp - INFO - عميل منقطع
2025-06-02 03:39:12,180 - FlaskApp - INFO - عميل متصل
2025-06-02 03:39:21,890 - MotorController.motor_2 - WARNING - المحرك motor_2 يعمل بالفعل
2025-06-02 03:39:27,110 - MotorController.motor_1 - INFO - تم تحديد اتجاه المحرك motor_1: خلف
2025-06-02 03:39:30,137 - MotorController.motor_1 - INFO - بدء تحريك المحرك motor_1: -103 خطوة
2025-06-02 03:39:31,579 - MotorController.motor_3 - INFO - تم تحديد اتجاه المحرك motor_3: خلف
2025-06-02 03:39:32,046 - MotorController.motor_3 - INFO - بدء تحريك المحرك motor_3: -198 خطوة
2025-06-02 03:39:33,068 - MotorController.motor_2 - INFO - انتهاء حركة المحرك motor_2 - الموضع الحالي: 3
2025-06-02 03:39:34,161 - MotorController.motor_1 - INFO - انتهاء حركة المحرك motor_1 - الموضع الحالي: 3
2025-06-02 13:02:44,799 - MotorController.motor_1 - WARNING - تشغيل في وضع المحاكاة - GPIO غير متوفر
2025-06-02 13:02:44,803 - MotorController.motor_2 - WARNING - تشغيل في وضع المحاكاة - GPIO غير متوفر
2025-06-02 13:02:44,805 - MotorController.motor_3 - WARNING - تشغيل في وضع المحاكاة - GPIO غير متوفر
2025-06-02 13:02:44,806 - SprayController - INFO - تم تهيئة نظام التحكم في الرش المحوري
2025-06-02 13:02:44,827 - FlaskApp - INFO - بدء تشغيل خادم نظام الرش المحوري
2025-06-02 13:02:44,860 - werkzeug - INFO -  * Restarting with stat
2025-06-02 13:02:48,495 - MotorController.motor_1 - WARNING - تشغيل في وضع المحاكاة - GPIO غير متوفر
2025-06-02 13:02:48,497 - MotorController.motor_2 - WARNING - تشغيل في وضع المحاكاة - GPIO غير متوفر
2025-06-02 13:02:48,506 - MotorController.motor_3 - WARNING - تشغيل في وضع المحاكاة - GPIO غير متوفر
2025-06-02 13:02:48,510 - SprayController - INFO - تم تهيئة نظام التحكم في الرش المحوري
2025-06-02 13:02:48,580 - FlaskApp - INFO - بدء تشغيل خادم نظام الرش المحوري
2025-06-02 13:02:48,588 - werkzeug - WARNING -  * Debugger is active!
2025-06-02 13:02:48,609 - werkzeug - INFO -  * Debugger PIN: 211-370-091

/* 
ملف الأنماط المخصص لنظام تشغيل المرشة المحورية
Custom CSS for Central Spray System
*/

/* إعدادات عامة للنص العربي */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* مؤشرات الحالة */
.status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin: 0 auto;
    border: 2px solid #fff;
    box-shadow: 0 0 5px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.status-indicator.active {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 5px rgba(0,0,0,0.3);
    }
    50% {
        box-shadow: 0 0 15px rgba(0,0,0,0.5);
    }
    100% {
        box-shadow: 0 0 5px rgba(0,0,0,0.3);
    }
}

/* حالات مختلفة للمؤشرات */
.status-indicator.system-active {
    background-color: #28a745;
}

.status-indicator.system-inactive {
    background-color: #6c757d;
}

.status-indicator.emergency-active {
    background-color: #dc3545;
    animation: blink 1s infinite;
}

.status-indicator.emergency-inactive {
    background-color: #28a745;
}

.status-indicator.motor-running {
    background-color: #007bff;
}

.status-indicator.motor-stopped {
    background-color: #6c757d;
}

.status-indicator.spray-active {
    background-color: #17a2b8;
}

.status-indicator.spray-inactive {
    background-color: #6c757d;
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

/* تحسينات للبطاقات */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-header {
    border-bottom: none;
    font-weight: 600;
}

/* تحسينات للأزرار */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.btn:active {
    transform: translateY(0);
}

/* أزرار التحكم في المحركات */
.motor-controls .btn {
    font-size: 0.875rem;
}

.motor-controls .btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
}

/* أزرار الرش */
.spray-toggle.active {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

/* حاوية الرسائل */
.alert-container {
    position: fixed;
    top: 80px;
    left: 20px;
    right: 20px;
    z-index: 1050;
    max-height: 300px;
    overflow-y: auto;
}

.alert {
    margin-bottom: 10px;
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسينات للنماذج */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* شريط التنقل */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

/* حالة الاتصال */
#connection-status {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

#connection-status.connected {
    background-color: #28a745 !important;
}

#connection-status.disconnected {
    background-color: #dc3545 !important;
    animation: blink 1s infinite;
}

/* تحسينات للجداول الصغيرة */
.table-sm th,
.table-sm td {
    padding: 0.5rem;
    font-size: 0.875rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .card-body {
        padding: 1rem 0.75rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    
    .status-indicator {
        width: 16px;
        height: 16px;
    }
    
    .alert-container {
        left: 10px;
        right: 10px;
    }
}

/* تحسينات للطباعة */
@media print {
    .btn,
    .alert-container,
    .navbar {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .status-indicator {
        border: 1px solid #000 !important;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات للوصولية */
.btn:focus,
.form-control:focus {
    outline: none;
}

.btn:focus-visible,
.form-control:focus-visible {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* ألوان مخصصة للحالات */
.text-success-custom {
    color: #28a745 !important;
}

.text-danger-custom {
    color: #dc3545 !important;
}

.text-warning-custom {
    color: #ffc107 !important;
}

.text-info-custom {
    color: #17a2b8 !important;
}

/* تحسينات للتفاعل */
.interactive-element {
    cursor: pointer;
    transition: all 0.3s ease;
}

.interactive-element:hover {
    transform: scale(1.05);
}

/* تنسيق خاص للأرقام */
.numeric-display {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 1.1em;
}

/* تحسينات للمساحات */
.section-spacing {
    margin-bottom: 2rem;
}

.element-spacing {
    margin-bottom: 1rem;
}

/**
 * ملف JavaScript الرئيسي لنظام تشغيل المرشة المحورية
 * Main JavaScript file for Central Spray System
 */

class SpraySystemController {
    constructor() {
        this.socket = null;
        this.systemStatus = null;
        this.operationStartTime = null;
        
        this.initializeSocket();
        this.bindEvents();
        this.loadInitialStatus();
    }

    /**
     * تهيئة اتصال Socket.IO
     */
    initializeSocket() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('متصل بالخادم');
            this.updateConnectionStatus(true);
        });

        this.socket.on('disconnect', () => {
            console.log('انقطع الاتصال بالخادم');
            this.updateConnectionStatus(false);
        });

        this.socket.on('status_update', (status) => {
            this.updateSystemStatus(status);
        });
    }

    /**
     * ربط الأحداث بالعناصر
     */
    bindEvents() {
        // أزرار التحكم في النظام
        document.getElementById('start-system').addEventListener('click', () => this.startSystem());
        document.getElementById('stop-system').addEventListener('click', () => this.stopSystem());
        document.getElementById('emergency-stop').addEventListener('click', () => this.emergencyStop());
        document.getElementById('reset-emergency').addEventListener('click', () => this.resetEmergency());

        // أزرار تحريك المحركات
        document.querySelectorAll('.motor-move').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const motor = e.target.dataset.motor;
                const steps = parseInt(e.target.dataset.steps);
                this.moveMotor(motor, steps);
            });
        });

        // أزرار الذهاب إلى موضع
        document.querySelectorAll('.motor-goto').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const motor = e.target.dataset.motor;
                const motorNum = motor.split('_')[1];
                const positionInput = document.getElementById(`motor${motorNum}-position-input`);
                const position = parseInt(positionInput.value);
                
                if (!isNaN(position)) {
                    this.moveMotorToPosition(motor, position);
                    positionInput.value = '';
                }
            });
        });

        // أزرار إيقاف المحركات
        document.querySelectorAll('.motor-stop').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const motor = e.target.dataset.motor;
                this.stopMotor(motor);
            });
        });

        // أزرار التحكم في الرش
        document.querySelectorAll('.spray-toggle').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const spray = e.target.dataset.spray;
                this.toggleSpray(spray, e.target);
            });
        });

        // زر إيقاف جميع المرشات
        document.getElementById('stop-all-spray').addEventListener('click', () => this.stopAllSpray());
    }

    /**
     * تحميل الحالة الأولية
     */
    async loadInitialStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            this.updateSystemStatus(status);
        } catch (error) {
            console.error('خطأ في تحميل الحالة:', error);
            this.showMessage('خطأ في تحميل حالة النظام', 'danger');
        }
    }

    /**
     * تحديث حالة الاتصال
     */
    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        if (connected) {
            statusElement.textContent = 'متصل';
            statusElement.className = 'badge bg-success connected';
        } else {
            statusElement.textContent = 'منقطع';
            statusElement.className = 'badge bg-danger disconnected';
        }
    }

    /**
     * تحديث حالة النظام
     */
    updateSystemStatus(status) {
        this.systemStatus = status;

        // تحديث حالة النظام العامة
        this.updateSystemIndicators(status);
        
        // تحديث حالة المحركات
        this.updateMotorStatus(status.motors);
        
        // تحديث حالة الرش
        this.updateSprayStatus(status.spray_status);
        
        // تحديث وقت التشغيل
        this.updateOperationTime(status.operation_start_time);
    }

    /**
     * تحديث مؤشرات النظام
     */
    updateSystemIndicators(status) {
        const systemStatus = document.getElementById('system-status');
        const emergencyStatus = document.getElementById('emergency-status');

        // حالة النظام
        if (status.system_active) {
            systemStatus.className = 'status-indicator system-active active';
        } else {
            systemStatus.className = 'status-indicator system-inactive';
        }

        // حالة الإيقاف الطارئ
        if (status.emergency_stop_active) {
            emergencyStatus.className = 'status-indicator emergency-active';
        } else {
            emergencyStatus.className = 'status-indicator emergency-inactive';
        }
    }

    /**
     * تحديث حالة المحركات
     */
    updateMotorStatus(motors) {
        Object.keys(motors).forEach(motorName => {
            const motor = motors[motorName];
            const motorNum = motorName.split('_')[1];
            
            const statusElement = document.getElementById(`motor${motorNum}-status`);
            const positionElement = document.getElementById(`motor${motorNum}-position`);
            
            if (motor.is_running) {
                statusElement.className = 'status-indicator motor-running active';
            } else {
                statusElement.className = 'status-indicator motor-stopped';
            }
            
            positionElement.textContent = motor.current_position;
        });
    }

    /**
     * تحديث حالة الرش
     */
    updateSprayStatus(sprayStatus) {
        Object.keys(sprayStatus).forEach(sprayName => {
            const isActive = sprayStatus[sprayName];
            const statusElement = document.getElementById(`${sprayName.replace('_', '')}-status`);
            const buttonElement = document.getElementById(`${sprayName.replace('_', '')}-toggle`);
            
            if (isActive) {
                statusElement.className = 'status-indicator spray-active active';
                buttonElement.classList.add('active');
                buttonElement.textContent = buttonElement.textContent.replace('تشغيل', 'إيقاف');
            } else {
                statusElement.className = 'status-indicator spray-inactive';
                buttonElement.classList.remove('active');
                buttonElement.textContent = buttonElement.textContent.replace('إيقاف', 'تشغيل');
            }
        });
    }

    /**
     * تحديث وقت التشغيل
     */
    updateOperationTime(startTime) {
        const timeElement = document.getElementById('operation-time');
        
        if (startTime && this.systemStatus?.system_active) {
            const start = new Date(startTime);
            const now = new Date();
            const diff = Math.floor((now - start) / 1000);
            
            const hours = Math.floor(diff / 3600);
            const minutes = Math.floor((diff % 3600) / 60);
            const seconds = diff % 60;
            
            timeElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            timeElement.textContent = '--:--:--';
        }
    }

    /**
     * بدء تشغيل النظام
     */
    async startSystem() {
        try {
            const response = await this.makeRequest('/api/system/start', 'POST');
            if (response.success) {
                this.showMessage('تم بدء تشغيل النظام بنجاح', 'success');
            } else {
                this.showMessage(response.message, 'danger');
            }
        } catch (error) {
            this.showMessage('خطأ في بدء تشغيل النظام', 'danger');
        }
    }

    /**
     * إيقاف النظام
     */
    async stopSystem() {
        try {
            const response = await this.makeRequest('/api/system/stop', 'POST');
            if (response.success) {
                this.showMessage('تم إيقاف النظام بنجاح', 'warning');
            } else {
                this.showMessage(response.message, 'danger');
            }
        } catch (error) {
            this.showMessage('خطأ في إيقاف النظام', 'danger');
        }
    }

    /**
     * إيقاف طارئ
     */
    async emergencyStop() {
        try {
            const response = await this.makeRequest('/api/system/emergency-stop', 'POST');
            if (response.success) {
                this.showMessage('تم تفعيل الإيقاف الطارئ', 'danger');
            } else {
                this.showMessage(response.message, 'danger');
            }
        } catch (error) {
            this.showMessage('خطأ في الإيقاف الطارئ', 'danger');
        }
    }

    /**
     * إعادة تعيين الإيقاف الطارئ
     */
    async resetEmergency() {
        try {
            const response = await this.makeRequest('/api/system/reset-emergency', 'POST');
            if (response.success) {
                this.showMessage('تم إعادة تعيين الإيقاف الطارئ', 'info');
            } else {
                this.showMessage(response.message, 'danger');
            }
        } catch (error) {
            this.showMessage('خطأ في إعادة تعيين الإيقاف الطارئ', 'danger');
        }
    }

    /**
     * تحريك محرك
     */
    async moveMotor(motorName, steps) {
        try {
            const response = await this.makeRequest('/api/motor/move', 'POST', {
                motor_name: motorName,
                steps: steps
            });
            
            if (response.success) {
                this.showMessage(`تم تحريك ${motorName} بـ ${steps} خطوة`, 'info');
            } else {
                this.showMessage(response.message, 'danger');
            }
        } catch (error) {
            this.showMessage('خطأ في تحريك المحرك', 'danger');
        }
    }

    /**
     * تحريك محرك إلى موضع
     */
    async moveMotorToPosition(motorName, position) {
        try {
            const response = await this.makeRequest('/api/motor/position', 'POST', {
                motor_name: motorName,
                position: position
            });
            
            if (response.success) {
                this.showMessage(`تم تحريك ${motorName} إلى الموضع ${position}`, 'info');
            } else {
                this.showMessage(response.message, 'danger');
            }
        } catch (error) {
            this.showMessage('خطأ في تحريك المحرك', 'danger');
        }
    }

    /**
     * إيقاف محرك
     */
    async stopMotor(motorName) {
        try {
            const response = await this.makeRequest('/api/motor/stop', 'POST', {
                motor_name: motorName
            });
            
            if (response.success) {
                this.showMessage(`تم إيقاف ${motorName}`, 'warning');
            } else {
                this.showMessage(response.message, 'danger');
            }
        } catch (error) {
            this.showMessage('خطأ في إيقاف المحرك', 'danger');
        }
    }

    /**
     * تبديل حالة الرش
     */
    async toggleSpray(sprayName, buttonElement) {
        const isActive = this.systemStatus?.spray_status[sprayName] || false;
        const action = isActive ? 'stop' : 'start';
        
        try {
            const response = await this.makeRequest(`/api/spray/${action}`, 'POST', {
                spray_name: sprayName
            });
            
            if (response.success) {
                this.showMessage(response.message, 'info');
            } else {
                this.showMessage(response.message, 'danger');
            }
        } catch (error) {
            this.showMessage('خطأ في التحكم بالرش', 'danger');
        }
    }

    /**
     * إيقاف جميع المرشات
     */
    async stopAllSpray() {
        try {
            const response = await this.makeRequest('/api/spray/stop-all', 'POST');
            if (response.success) {
                this.showMessage('تم إيقاف جميع المرشات', 'warning');
            } else {
                this.showMessage(response.message, 'danger');
            }
        } catch (error) {
            this.showMessage('خطأ في إيقاف المرشات', 'danger');
        }
    }

    /**
     * إرسال طلب HTTP
     */
    async makeRequest(url, method, data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);
        return await response.json();
    }

    /**
     * عرض رسالة للمستخدم
     */
    showMessage(message, type = 'info') {
        const container = document.getElementById('messages');
        const alertDiv = document.createElement('div');
        
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        container.appendChild(alertDiv);
        
        // إزالة الرسالة تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.spraySystem = new SpraySystemController();
    
    // تحديث وقت التشغيل كل ثانية
    setInterval(() => {
        if (window.spraySystem.systemStatus?.operation_start_time) {
            window.spraySystem.updateOperationTime(window.spraySystem.systemStatus.operation_start_time);
        }
    }, 1000);
});

"""
التطبيق الرئيسي لنظام تشغيل المرشة المحورية
Main Flask Application for Central Spray System
"""

import logging
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from flask_socketio import SocketIO, emit
import threading
import time

from spray_controller import SprayController
from config import Config

# إعداد التسجيل
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# إنشاء التطبيق
app = Flask(__name__)
app.config.from_object(Config)

# إعداد SocketIO للتحديثات المباشرة
socketio = SocketIO(app, cors_allowed_origins="*")

# إنشاء وحدة التحكم
spray_controller = SprayController()

# متغير لتتبع حالة البث
status_broadcast_active = False
status_thread = None

logger = logging.getLogger('FlaskApp')

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    return render_template('index.html')

@app.route('/api/system/start', methods=['POST'])
def start_system():
    """بدء تشغيل النظام"""
    try:
        success = spray_controller.start_system()
        if success:
            start_status_broadcast()
        return jsonify({
            'success': success,
            'message': 'تم بدء تشغيل النظام بنجاح' if success else 'فشل في بدء تشغيل النظام'
        })
    except Exception as e:
        logger.error(f"خطأ في بدء النظام: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/system/stop', methods=['POST'])
def stop_system():
    """إيقاف النظام"""
    try:
        spray_controller.stop_system()
        stop_status_broadcast()
        return jsonify({
            'success': True,
            'message': 'تم إيقاف النظام بنجاح'
        })
    except Exception as e:
        logger.error(f"خطأ في إيقاف النظام: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/system/emergency-stop', methods=['POST'])
def emergency_stop():
    """إيقاف طارئ"""
    try:
        spray_controller.emergency_stop()
        stop_status_broadcast()
        return jsonify({
            'success': True,
            'message': 'تم تفعيل الإيقاف الطارئ'
        })
    except Exception as e:
        logger.error(f"خطأ في الإيقاف الطارئ: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/system/reset-emergency', methods=['POST'])
def reset_emergency():
    """إعادة تعيين الإيقاف الطارئ"""
    try:
        spray_controller.reset_emergency_stop()
        return jsonify({
            'success': True,
            'message': 'تم إعادة تعيين الإيقاف الطارئ'
        })
    except Exception as e:
        logger.error(f"خطأ في إعادة تعيين الإيقاف الطارئ: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/motor/move', methods=['POST'])
def move_motor():
    """تحريك محرك"""
    try:
        data = request.get_json()
        motor_name = data.get('motor_name')
        steps = data.get('steps', 0)
        speed = data.get('speed')
        
        success = spray_controller.move_motor(motor_name, steps, speed)
        return jsonify({
            'success': success,
            'message': f'تم تحريك المحرك {motor_name}' if success else f'فشل في تحريك المحرك {motor_name}'
        })
    except Exception as e:
        logger.error(f"خطأ في تحريك المحرك: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/motor/position', methods=['POST'])
def move_motor_to_position():
    """تحريك محرك إلى موضع محدد"""
    try:
        data = request.get_json()
        motor_name = data.get('motor_name')
        position = data.get('position', 0)
        speed = data.get('speed')
        
        success = spray_controller.move_motor_to_position(motor_name, position, speed)
        return jsonify({
            'success': success,
            'message': f'تم تحريك المحرك {motor_name} إلى الموضع {position}' if success else f'فشل في تحريك المحرك {motor_name}'
        })
    except Exception as e:
        logger.error(f"خطأ في تحريك المحرك: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/motor/stop', methods=['POST'])
def stop_motor():
    """إيقاف محرك"""
    try:
        data = request.get_json()
        motor_name = data.get('motor_name')
        
        success = spray_controller.stop_motor(motor_name)
        return jsonify({
            'success': success,
            'message': f'تم إيقاف المحرك {motor_name}' if success else f'فشل في إيقاف المحرك {motor_name}'
        })
    except Exception as e:
        logger.error(f"خطأ في إيقاف المحرك: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/spray/start', methods=['POST'])
def start_spray():
    """بدء الرش"""
    try:
        data = request.get_json()
        spray_name = data.get('spray_name')
        
        success = spray_controller.start_spray(spray_name)
        return jsonify({
            'success': success,
            'message': f'تم بدء الرش من {spray_name}' if success else f'فشل في بدء الرش من {spray_name}'
        })
    except Exception as e:
        logger.error(f"خطأ في بدء الرش: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/spray/stop', methods=['POST'])
def stop_spray():
    """إيقاف الرش"""
    try:
        data = request.get_json()
        spray_name = data.get('spray_name')
        
        success = spray_controller.stop_spray(spray_name)
        return jsonify({
            'success': success,
            'message': f'تم إيقاف الرش من {spray_name}' if success else f'فشل في إيقاف الرش من {spray_name}'
        })
    except Exception as e:
        logger.error(f"خطأ في إيقاف الرش: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/spray/stop-all', methods=['POST'])
def stop_all_spray():
    """إيقاف جميع المرشات"""
    try:
        spray_controller.stop_all_spray()
        return jsonify({
            'success': True,
            'message': 'تم إيقاف جميع المرشات'
        })
    except Exception as e:
        logger.error(f"خطأ في إيقاف جميع المرشات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/status')
def get_status():
    """الحصول على حالة النظام"""
    try:
        status = spray_controller.get_system_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"خطأ في الحصول على الحالة: {e}")
        return jsonify({'error': str(e)}), 500

def status_broadcast_worker():
    """دالة بث حالة النظام"""
    global status_broadcast_active
    while status_broadcast_active:
        try:
            status = spray_controller.get_system_status()
            socketio.emit('status_update', status)
            time.sleep(1)  # تحديث كل ثانية
        except Exception as e:
            logger.error(f"خطأ في بث الحالة: {e}")
            time.sleep(5)

def start_status_broadcast():
    """بدء بث حالة النظام"""
    global status_broadcast_active, status_thread
    if not status_broadcast_active:
        status_broadcast_active = True
        status_thread = threading.Thread(target=status_broadcast_worker)
        status_thread.daemon = True
        status_thread.start()
        logger.info("تم بدء بث حالة النظام")

def stop_status_broadcast():
    """إيقاف بث حالة النظام"""
    global status_broadcast_active
    status_broadcast_active = False
    logger.info("تم إيقاف بث حالة النظام")

@socketio.on('connect')
def handle_connect():
    """معالج الاتصال"""
    logger.info('عميل متصل')
    emit('status_update', spray_controller.get_system_status())

@socketio.on('disconnect')
def handle_disconnect():
    """معالج قطع الاتصال"""
    logger.info('عميل منقطع')

if __name__ == '__main__':
    try:
        logger.info("بدء تشغيل خادم نظام الرش المحوري")
        socketio.run(
            app, 
            host=Config.HOST, 
            port=Config.PORT, 
            debug=Config.DEBUG
        )
    except KeyboardInterrupt:
        logger.info("تم إيقاف الخادم بواسطة المستخدم")
    finally:
        spray_controller.cleanup()
        logger.info("تم تنظيف الموارد")

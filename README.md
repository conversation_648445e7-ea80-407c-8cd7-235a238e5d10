# نظام تشغيل المرشة المحورية
## Central Spray System

نظام تحكم متقدم لتشغيل المرشة المحورية مع المحركات التابعة لها عن بُعد باستخدام Python وواجهة ويب تفاعلية.

### الميزات الرئيسية

- **التحكم عن بُعد**: واجهة ويب تفاعلية للتحكم الكامل في النظام
- **تحكم في المحركات**: تحكم دقيق في 3 محركات خطوية مع إمكانية تحديد المواضع والسرعات
- **نظام الرش**: تحكم في المضخة و4 صمامات رش منفصلة
- **الأمان**: نظام إيقاف طارئ ومراقبة وقت التشغيل
- **التحديثات المباشرة**: عرض حالة النظام في الوقت الفعلي باستخدام WebSocket
- **واجهة عربية**: تصميم متجاوب باللغة العربية باستخدام Bootstrap

### متطلبات النظام

- Python 3.7 أو أحدث
- Raspberry Pi (للتشغيل الفعلي) أو أي نظام Linux/Windows (للمحاكاة)
- متصفح ويب حديث

### التثبيت

1. **استنساخ المشروع**:
```bash
git clone <repository-url>
cd spray-system
```

2. **إنشاء بيئة افتراضية**:
```bash
python -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\Scripts\activate     # على Windows
```

3. **تثبيت المتطلبات**:
```bash
pip install -r requirements.txt
```

4. **تكوين الإعدادات**:
   - تحرير ملف `.env` لتخصيص الإعدادات
   - تحرير ملف `config.py` لتحديد منافذ GPIO

### التشغيل

```bash
python app.py
```

ثم افتح المتصفح وانتقل إلى: `http://localhost:5000`

### هيكل المشروع

```
spray-system/
├── app.py                 # التطبيق الرئيسي Flask
├── config.py             # إعدادات النظام
├── spray_controller.py   # وحدة التحكم في الرش
├── motor_controller.py   # وحدة التحكم في المحركات
├── requirements.txt      # المتطلبات
├── .env                 # متغيرات البيئة
├── templates/
│   └── index.html       # الواجهة الرئيسية
├── static/
│   ├── css/
│   │   └── style.css    # ملف الأنماط
│   └── js/
│       └── app.js       # JavaScript للتفاعل
└── README.md
```

### واجهة برمجة التطبيقات (API)

#### التحكم في النظام
- `POST /api/system/start` - بدء تشغيل النظام
- `POST /api/system/stop` - إيقاف النظام
- `POST /api/system/emergency-stop` - إيقاف طارئ
- `POST /api/system/reset-emergency` - إعادة تعيين الإيقاف الطارئ

#### التحكم في المحركات
- `POST /api/motor/move` - تحريك محرك عدد معين من الخطوات
- `POST /api/motor/position` - تحريك محرك إلى موضع محدد
- `POST /api/motor/stop` - إيقاف محرك

#### التحكم في الرش
- `POST /api/spray/start` - بدء الرش من مرشة محددة
- `POST /api/spray/stop` - إيقاف الرش من مرشة محددة
- `POST /api/spray/stop-all` - إيقاف جميع المرشات

#### الحالة
- `GET /api/status` - الحصول على حالة النظام الكاملة

### إعدادات GPIO (Raspberry Pi)

#### المحركات الخطوية
- **المحرك 1**: Step=18, Direction=19, Enable=20
- **المحرك 2**: Step=21, Direction=22, Enable=23
- **المحرك 3**: Step=24, Direction=25, Enable=26

#### نظام الرش
- **المضخة**: GPIO 12
- **صمام 1**: GPIO 13
- **صمام 2**: GPIO 14
- **صمام 3**: GPIO 15
- **صمام 4**: GPIO 16

#### الأمان
- **زر الإيقاف الطارئ**: GPIO 17

### الأمان

- **إيقاف طارئ**: زر فيزيائي وزر في الواجهة
- **مراقبة الوقت**: إيقاف تلقائي بعد ساعة واحدة
- **حماية المحركات**: تعطيل تلقائي عند الإيقاف
- **تسجيل العمليات**: تسجيل جميع الأحداث في ملف السجل

### استكشاف الأخطاء

1. **خطأ GPIO**: إذا كنت لا تستخدم Raspberry Pi، سيعمل النظام في وضع المحاكاة
2. **مشاكل الاتصال**: تحقق من إعدادات الشبكة والمنافذ
3. **أخطاء المحركات**: تحقق من توصيلات GPIO والطاقة

### التطوير

لإضافة ميزات جديدة:

1. **إضافة API جديد**: أضف route في `app.py`
2. **تحديث الواجهة**: عدّل `templates/index.html` و `static/js/app.js`
3. **إضافة محرك جديد**: حدّث `config.py` و `spray_controller.py`

### الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

### الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

---

**ملاحظة**: تأكد من اتباع إجراءات الأمان المناسبة عند التعامل مع الأجهزة الكهربائية والمحركات.

"""
إعدادات برنامج تشغيل المرشة المحورية
Configuration for Central Spray System
"""

import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # إعدادات Flask
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'spray-system-secret-key-2024'
    DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'
    HOST = os.environ.get('HOST', '0.0.0.0')
    PORT = int(os.environ.get('PORT', 5000))
    
    # إعدادات المحركات (GPIO pins)
    MOTOR_PINS = {
        'motor_1': {
            'step': 18,
            'direction': 19,
            'enable': 20
        },
        'motor_2': {
            'step': 21,
            'direction': 22,
            'enable': 23
        },
        'motor_3': {
            'step': 24,
            'direction': 25,
            'enable': 26
        }
    }
    
    # إعدادات المرشة
    SPRAY_PINS = {
        'pump': 12,
        'valve_1': 13,
        'valve_2': 14,
        'valve_3': 15,
        'valve_4': 16
    }
    
    # إعدادات الحركة
    DEFAULT_SPEED = 100  # خطوات في الثانية
    MAX_SPEED = 1000
    MIN_SPEED = 10
    
    # إعدادات الأمان
    MAX_OPERATION_TIME = 3600  # ثانية (ساعة واحدة)
    EMERGENCY_STOP_PIN = 17
    
    # إعدادات التسجيل
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'spray_system.log'

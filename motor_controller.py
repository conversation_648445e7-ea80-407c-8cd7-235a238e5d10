"""
وحدة التحكم في المحركات
Motor Controller Module for Central Spray System
"""

import time
import threading
from typing import Dict, Optional
import logging

try:
    import RPi.GPIO as GPIO
    GPIO_AVAILABLE = True
except ImportError:
    GPIO_AVAILABLE = False
    print("تحذير: مكتبة GPIO غير متوفرة - سيتم استخدام وضع المحاكاة")

from config import Config

class MotorController:
    """فئة التحكم في المحركات الخطوية"""
    
    def __init__(self, motor_name: str, pins: Dict[str, int]):
        self.motor_name = motor_name
        self.step_pin = pins['step']
        self.direction_pin = pins['direction']
        self.enable_pin = pins['enable']
        
        self.is_running = False
        self.current_position = 0
        self.target_position = 0
        self.speed = Config.DEFAULT_SPEED
        self.direction = 1  # 1 للأمام، -1 للخلف
        
        self.movement_thread = None
        self.stop_event = threading.Event()
        
        self.logger = logging.getLogger(f'MotorController.{motor_name}')
        
        if GPIO_AVAILABLE:
            self._setup_gpio()
        else:
            self.logger.warning("تشغيل في وضع المحاكاة - GPIO غير متوفر")
    
    def _setup_gpio(self):
        """إعداد منافذ GPIO"""
        GPIO.setmode(GPIO.BCM)
        GPIO.setup(self.step_pin, GPIO.OUT)
        GPIO.setup(self.direction_pin, GPIO.OUT)
        GPIO.setup(self.enable_pin, GPIO.OUT)
        
        # تفعيل المحرك
        GPIO.output(self.enable_pin, GPIO.LOW)
        self.logger.info(f"تم إعداد GPIO للمحرك {self.motor_name}")
    
    def set_direction(self, direction: int):
        """تحديد اتجاه الحركة"""
        self.direction = 1 if direction > 0 else -1
        if GPIO_AVAILABLE:
            GPIO.output(self.direction_pin, GPIO.HIGH if self.direction > 0 else GPIO.LOW)
        self.logger.info(f"تم تحديد اتجاه المحرك {self.motor_name}: {'أمام' if self.direction > 0 else 'خلف'}")
    
    def set_speed(self, speed: int):
        """تحديد سرعة المحرك"""
        self.speed = max(Config.MIN_SPEED, min(Config.MAX_SPEED, speed))
        self.logger.info(f"تم تحديد سرعة المحرك {self.motor_name}: {self.speed} خطوة/ثانية")
    
    def move_steps(self, steps: int, speed: Optional[int] = None):
        """تحريك المحرك عدد معين من الخطوات"""
        if self.is_running:
            self.logger.warning(f"المحرك {self.motor_name} يعمل بالفعل")
            return False
        
        if speed:
            self.set_speed(speed)
        
        self.target_position = self.current_position + steps
        self.set_direction(1 if steps > 0 else -1)
        
        self.stop_event.clear()
        self.movement_thread = threading.Thread(
            target=self._move_worker, 
            args=(abs(steps),)
        )
        self.movement_thread.start()
        
        self.logger.info(f"بدء تحريك المحرك {self.motor_name}: {steps} خطوة")
        return True
    
    def move_to_position(self, position: int, speed: Optional[int] = None):
        """تحريك المحرك إلى موضع محدد"""
        steps = position - self.current_position
        return self.move_steps(steps, speed)
    
    def _move_worker(self, steps: int):
        """دالة العمل لتحريك المحرك"""
        self.is_running = True
        delay = 1.0 / (2 * self.speed)  # تأخير بين النبضات
        
        try:
            for _ in range(steps):
                if self.stop_event.is_set():
                    break
                
                if GPIO_AVAILABLE:
                    GPIO.output(self.step_pin, GPIO.HIGH)
                    time.sleep(delay)
                    GPIO.output(self.step_pin, GPIO.LOW)
                    time.sleep(delay)
                else:
                    # محاكاة الحركة
                    time.sleep(delay * 2)
                
                self.current_position += self.direction
                
        except Exception as e:
            self.logger.error(f"خطأ في تحريك المحرك {self.motor_name}: {e}")
        finally:
            self.is_running = False
            self.logger.info(f"انتهاء حركة المحرك {self.motor_name} - الموضع الحالي: {self.current_position}")
    
    def stop(self):
        """إيقاف المحرك"""
        if self.is_running:
            self.stop_event.set()
            if self.movement_thread:
                self.movement_thread.join(timeout=2)
            self.logger.info(f"تم إيقاف المحرك {self.motor_name}")
    
    def emergency_stop(self):
        """إيقاف طارئ للمحرك"""
        self.stop()
        if GPIO_AVAILABLE:
            GPIO.output(self.enable_pin, GPIO.HIGH)  # تعطيل المحرك
        self.logger.warning(f"إيقاف طارئ للمحرك {self.motor_name}")
    
    def enable(self):
        """تفعيل المحرك"""
        if GPIO_AVAILABLE:
            GPIO.output(self.enable_pin, GPIO.LOW)
        self.logger.info(f"تم تفعيل المحرك {self.motor_name}")
    
    def disable(self):
        """تعطيل المحرك"""
        if GPIO_AVAILABLE:
            GPIO.output(self.enable_pin, GPIO.HIGH)
        self.logger.info(f"تم تعطيل المحرك {self.motor_name}")
    
    def get_status(self) -> Dict:
        """الحصول على حالة المحرك"""
        return {
            'name': self.motor_name,
            'is_running': self.is_running,
            'current_position': self.current_position,
            'target_position': self.target_position,
            'speed': self.speed,
            'direction': self.direction
        }
    
    def cleanup(self):
        """تنظيف الموارد"""
        self.stop()
        if GPIO_AVAILABLE:
            GPIO.cleanup([self.step_pin, self.direction_pin, self.enable_pin])
        self.logger.info(f"تم تنظيف موارد المحرك {self.motor_name}")
